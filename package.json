{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build && node copy-new-favicon.js", "build:dev": "vite build --mode development && node copy-new-favicon.js", "build:uat": "vite build --mode uat && node copy-new-favicon.js", "build:prod": "vite build --mode production && node copy-new-favicon.js", "lint": "eslint .", "lint:fix": "eslint . --fix", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "preview": "vite preview", "update-favicon": "node update-favicon.js", "copy-new-favicon": "node copy-new-favicon.js", "deploy:dev": "npm run build:dev", "deploy:uat": "npm run build:uat", "deploy:prod": "npm run build:prod", "supabase:start": "supabase start", "supabase:stop": "supabase stop", "supabase:reset": "supabase db reset", "supabase:migrate": "supabase db push", "supabase:deploy-functions": "./deploy-sms-functions.sh"}, "dependencies": {"@googlemaps/react-wrapper": "^1.2.0", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.56.2", "@tanstack/react-table": "^8.21.2", "@tanstack/react-virtual": "^3.13.6", "@types/bcryptjs": "^2.4.6", "@types/file-saver": "^2.0.7", "@types/leaflet": "^1.9.18", "@types/papaparse": "^5.3.15", "@zxing/browser": "^0.1.4", "@zxing/library": "^0.20.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "file-saver": "^2.0.5", "framer-motion": "^12.11.3", "i18next": "^24.2.3", "i18next-browser-languagedetector": "^8.0.4", "input-otp": "^1.2.4", "leaflet": "^1.9.4", "lucide-react": "^0.462.0", "next-themes": "^0.3.0", "papaparse": "^5.5.2", "pg": "^8.16.3", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-i18next": "^15.4.1", "react-leaflet": "^4.2.1", "react-photo-view": "^1.2.7", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "react-use": "^17.6.0", "recharts": "^2.12.7", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.3", "xlsx": "^0.18.5", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.15", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "jsdom": "^26.1.0", "lovable-tagger": "^1.1.7", "postcss": "^8.4.47", "sharp": "^0.34.1", "tailwindcss": "^3.4.11", "terser": "^5.43.1", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1", "vitest": "^3.2.4"}}
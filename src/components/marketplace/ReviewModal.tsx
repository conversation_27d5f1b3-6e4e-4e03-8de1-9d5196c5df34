/**
 * Review Modal - Consumer Review Submission
 * 
 * Modal for submitting product reviews with rating and text
 * 🎯 CRITICAL: Preserves existing authentication system - integrates with phone auth
 */

import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { Star, Loader2, CheckCircle } from 'lucide-react';
import { createReview } from '@/services/reviewsService';

interface ReviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  productId: string;
  productName: string;
  onReviewSubmitted?: (newReview?: any) => void;
}

export function ReviewModal({
  isOpen,
  onClose,
  productId,
  productName,
  onReviewSubmitted
}: ReviewModalProps) {
  const { t } = useTranslation();
  const { toast } = useToast();
  
  const [rating, setRating] = useState(0);
  const [hoveredRating, setHoveredRating] = useState(0);
  const [title, setTitle] = useState('');
  const [reviewText, setReviewText] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  // Reset form when modal opens/closes
  React.useEffect(() => {
    if (!isOpen) {
      setRating(0);
      setHoveredRating(0);
      setTitle('');
      setReviewText('');
      setIsSubmitting(false);
      setShowSuccess(false);
    }
  }, [isOpen]);

  // Handle star click
  const handleStarClick = (starRating: number) => {
    setRating(starRating);
  };

  // Handle star hover
  const handleStarHover = (starRating: number) => {
    setHoveredRating(starRating);
  };

  // Handle star hover leave
  const handleStarLeave = () => {
    setHoveredRating(0);
  };

  // Handle form submission with optimized performance
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (rating === 0) {
      toast.error(t('validation.ratingRequired'), {
        description: t('validation.ratingRequiredDescription')
      });
      return;
    }

    // Only rating is required - title and text are optional
    // Removed the validation that required either title or text

    // Prevent multiple submissions
    if (isSubmitting) {
      return;
    }

    setIsSubmitting(true);

    try {
      const result = await createReview({
        product_id: productId,
        product_name: productName,
        rating,
        title: title.trim() || undefined,
        review_text: reviewText.trim() || undefined
      });

      if (result.success) {
        // Reset loading state and show success
        setIsSubmitting(false);
        setShowSuccess(true);

        // Show enhanced success toast
        toast.success(t('marketplace.reviewAddedSuccessfully'), {
          description: t('marketplace.reviewAddedSuccessfullyDescription')
        });

        // Refresh reviews immediately for instant feedback
        onReviewSubmitted?.(result.review);

        // Close modal after showing success for 1.5 seconds
        setTimeout(() => {
          onClose();
        }, 1500);
      } else {
        toast.error(t('validation.reviewSubmissionFailed'), {
          description: result.error || t('validation.reviewSubmissionFailedDescription')
        });
        setIsSubmitting(false); // Re-enable form on error
      }
    } catch (error) {
      console.error('Error submitting review:', error);
      toast.error(t('validation.reviewSubmissionFailed'), {
        description: t('validation.reviewSubmissionFailedDescription')
      });
      setIsSubmitting(false); // Re-enable form on error
    }
    // Note: We don't set isSubmitting to false on success because the modal closes
  }, [rating, title, reviewText, isSubmitting, productId, productName, t, toast, onClose, onReviewSubmitted]);

  // Get rating text
  const getRatingText = (rating: number) => {
    switch (rating) {
      case 1: return 'Poor';
      case 2: return 'Fair';
      case 3: return 'Good';
      case 4: return 'Very Good';
      case 5: return 'Excellent';
      default: return 'Select Rating';
    }
  };

  const displayRating = hoveredRating || rating;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        {showSuccess ? (
          // Success State
          <div className="text-center py-8">
            <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              {t('marketplace.reviewAddedSuccessfully')}
            </h3>
            <p className="text-gray-600 mb-6">
              {t('marketplace.reviewAddedSuccessfullyDescription')}
            </p>
            <div className="flex justify-center">
              <div className="animate-pulse flex space-x-1">
                <div className="w-2 h-2 bg-[#fa7b00] rounded-full"></div>
                <div className="w-2 h-2 bg-[#fa7b00] rounded-full animation-delay-200"></div>
                <div className="w-2 h-2 bg-[#fa7b00] rounded-full animation-delay-400"></div>
              </div>
            </div>
          </div>
        ) : (
          // Form State
          <>
            <DialogHeader>
              <DialogTitle className="text-xl font-semibold">
                Write a Review
              </DialogTitle>
              <DialogDescription>
                Share your experience with <span className="font-medium">{productName}</span>
              </DialogDescription>
            </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Star Rating */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Rating *</Label>
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1">
                {[1, 2, 3, 4, 5].map((star) => (
                  <button
                    key={star}
                    type="button"
                    className="p-1 rounded-sm hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    onClick={() => handleStarClick(star)}
                    onMouseEnter={() => handleStarHover(star)}
                    onMouseLeave={handleStarLeave}
                    disabled={isSubmitting}
                  >
                    <Star
                      className={`h-8 w-8 transition-colors ${
                        star <= displayRating
                          ? 'fill-yellow-400 text-yellow-400'
                          : 'fill-gray-200 text-gray-200 hover:fill-yellow-200 hover:text-yellow-200'
                      }`}
                    />
                  </button>
                ))}
              </div>
              <span className="text-sm text-gray-600 ml-2">
                {getRatingText(displayRating)}
              </span>
            </div>
          </div>

          {/* Review Title */}
          <div className="space-y-2">
            <Label htmlFor="review-title" className="text-sm font-medium">
              Review Title (Optional)
            </Label>
            <Input
              id="review-title"
              placeholder="Summarize your experience..."
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              maxLength={100}
              disabled={isSubmitting}
            />
            <p className="text-xs text-gray-500">
              {title.length}/100 characters
            </p>
          </div>

          {/* Review Text */}
          <div className="space-y-2">
            <Label htmlFor="review-text" className="text-sm font-medium">
              Your Review (Optional)
            </Label>
            <Textarea
              id="review-text"
              placeholder="Tell others about your experience with this product..."
              value={reviewText}
              onChange={(e) => setReviewText(e.target.value)}
              rows={4}
              maxLength={1000}
              disabled={isSubmitting}
              className="resize-none"
            />
            <p className="text-xs text-gray-500">
              {reviewText.length}/1000 characters
            </p>
          </div>

          <DialogFooter className="gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || rating === 0}
              className="bg-[#fa7b00] hover:bg-[#fa7b00]/90 text-white"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Submitting...
                </>
              ) : (
                'Submit Review'
              )}
            </Button>
          </DialogFooter>
        </form>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}

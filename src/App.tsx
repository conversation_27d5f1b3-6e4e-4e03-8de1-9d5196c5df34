import React, { useState, Suspense, lazy } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { CartProvider } from "@/contexts/CartContext";
import { UserProvider } from "@/contexts/UserContext";
import { AuthProvider } from "@/contexts/AuthContext";
import { LocationProvider } from "@/contexts/LocationContext";

// Loading component
import { Loader2 } from "lucide-react";

const LoadingSpinner = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="flex flex-col items-center gap-2">
      <Loader2 className="h-8 w-8 animate-spin text-primary" />
      <span className="text-sm text-muted-foreground">Loading...</span>
    </div>
  </div>
);

// Core marketplace components (loaded immediately for fast initial render)
import MarketplaceContainer from "./pages/marketplace/MarketplaceContainer";
import PartnersPage from "./pages/marketplace/PartnersPage";

// Lazy load heavy components for better performance
const ProductPage = lazy(() => import("./pages/ProductPage"));
const BasketPage = lazy(() => import("./pages/BasketPage").then(module => ({ default: module.BasketPage })));
const OrderSuccessPage = lazy(() => import("./pages/OrderSuccessPage").then(module => ({ default: module.OrderSuccessPage })));
const WishlistPage = lazy(() => import("./pages/WishlistPage"));
const MyReviewsPage = lazy(() => import("./pages/MyReviewsPage"));
const MyOrdersPage = lazy(() => import("./pages/MyOrdersPage"));
const AboutPage = lazy(() => import("./pages/AboutPage"));
const HelpPage = lazy(() => import("./pages/HelpPage"));

// Lazy load marketplace sub-components
const MarketplaceHome = lazy(() => import("./pages/marketplace/MarketplaceHome"));
const MyVehicleParts = lazy(() => import("./pages/marketplace/MyVehicleParts"));
const WholesaleOffers = lazy(() => import("./pages/marketplace/WholesaleOffers"));
// Lazy load test/debug pages (only loaded when needed)
const Test = lazy(() => import("./test"));
const ProductTestPage = lazy(() => import("./pages/ProductTestPage"));
const ProductDebugPage = lazy(() => import("./pages/ProductDebugPage"));
const ProductPageTest = lazy(() => import("./pages/ProductPageTest"));
const ProductIdMigrationPage = lazy(() => import("./pages/ProductIdMigrationPage"));
const SupabaseMigrationPage = lazy(() => import("./pages/SupabaseMigrationPage"));
const SupabaseTestPage = lazy(() => import("./pages/SupabaseTestPage"));
const SystemDiagnosticsPage = lazy(() => import("./pages/SystemDiagnosticsPage"));
const ProductStatusTest = lazy(() => import("./pages/debug/ProductStatusTest").then(module => ({ default: module.ProductStatusTest })));
const ProductPageDiagnostic = lazy(() => import("./pages/ProductPageDiagnostic"));
const ProductPageComprehensiveTest = lazy(() => import("./pages/ProductPageComprehensiveTest"));
const CategoryMigrationTestPage = lazy(() => import("./pages/CategoryMigrationTestPage"));
const LocationSystemTestPage = lazy(() => import("./pages/LocationSystemTestPage"));

// Lazy load supplier/shipping pages
const SupplierOrdersPage = lazy(() => import("./pages/SupplierOrdersPage"));
const SupplierOrderDetailPage = lazy(() => import("./pages/SupplierOrderDetailPage"));
const SupplierShipmentsPage = lazy(() => import("./pages/SupplierShipmentsPage"));
const ShippingLoginPage = lazy(() => import("./pages/ShippingLoginPage"));
const ShippingDashboardPage = lazy(() => import("./pages/ShippingDashboardPage"));
const ShippingOrderDetailPage = lazy(() => import("./pages/ShippingOrderDetailPage"));
const OrderSystemDebugPage = lazy(() => import("./pages/OrderSystemDebugPage"));
const SimpleTestPage = lazy(() => import("./pages/SimpleTestPage"));
// ADMIN PANEL TEMPORARILY DISABLED - DO NOT DELETE THESE IMPORTS
// import AdminLoginPage from "./pages/AdminLoginPage";
// import AdminDashboardPage from "./pages/AdminDashboardPage";
// import AdminOrdersPage from "./pages/AdminOrdersPage";
// import AdminShippingCompaniesPage from "./pages/AdminShippingCompaniesPage";
// import AdminShipmentsPage from "./pages/AdminShipmentsPage";
// import AdminAnalyticsPage from "./pages/AdminAnalyticsPage";
// import AdminUsersPage from "./pages/AdminUsersPage";
// import AdminSettingsPage from "./pages/AdminSettingsPage";

// Lazy load more test pages
const TestNorthwestFix = lazy(() => import("./pages/test-northwest-fix"));
const GoogleMapsTest = lazy(() => import("./pages/GoogleMapsTest").then(module => ({ default: module.GoogleMapsTest })));
const MapComparisonDemo = lazy(() => import("./pages/MapComparisonDemo").then(module => ({ default: module.MapComparisonDemo })));
const PhoneAuthTest = lazy(() => import("./pages/PhoneAuthTest").then(module => ({ default: module.PhoneAuthTest })));
const OTPTestPage = lazy(() => import("./pages/OTPTestPage"));
const TestShippingAssignment = lazy(() => import("./pages/TestShippingAssignment"));
const OrderSecurityTestPage = lazy(() => import("./pages/OrderSecurityTestPage"));
const ConsumerPasscodeTest = lazy(() => import("./pages/test/ConsumerPasscodeTest"));
const ConsumerAuthTest = lazy(() => import("./components/test/ConsumerAuthTest"));
const AuthenticationFixTest = lazy(() => import("./pages/test/AuthenticationFixTest"));

// Lazy load protected app pages
const Index = lazy(() => import("./pages/Index"));
const ProductsPage = lazy(() => import("./pages/ProductsPage"));
const ProductsDataGridPage = lazy(() => import("./pages/ProductsDataGridPage"));
const UnifiedProductFormPage = lazy(() => import("./pages/UnifiedProductFormPage"));
const CategoryProductsPage = lazy(() => import("./pages/CategoryProductsPage"));
const CompatibilityHub = lazy(() => import("./pages/CompatibilityHub"));
const SettingsPage = lazy(() => import("./pages/SettingsPage"));

// Lazy load auth pages
const AuthCallbackPage = lazy(() => import("./pages/AuthCallbackPage"));
const ResetPasswordPage = lazy(() => import("./pages/ResetPasswordPage"));

// Lazy load debug page
const DebugPage = lazy(() => import("./pages/DebugPage").then(module => ({ default: module.DebugPage })));

// Immediately loaded components (critical for initial render)
import NotFound from "./pages/NotFound";
import AccessDeniedPage from "./pages/AccessDeniedPage";

// Auth Components
import { LogoutConfirmationModal } from "./components/common/LogoutConfirmationModal";
import { AutoAuthModal } from "./components/auth/AutoAuthModal";

// Components
import { SecureRoute } from "@/components/auth/SecureRoute";
import { ErrorBoundary } from "@/components/ErrorBoundary";
import { AutoLocationDetector } from "@/components/location";

const App = () => {
  // Create a client inside the component to ensure React context works correctly
  const [queryClient] = useState(() => new QueryClient());

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <AuthProvider>
          <UserProvider>
            <LocationProvider>
              <CartProvider>
                <TooltipProvider>
                <Toaster />
                <Sonner />
                {/* Auto Location Detector - triggers GPS detection on first visit */}
                <AutoLocationDetector />
                <Suspense fallback={<LoadingSpinner />}>
                  <Routes>
                    {/* Test Routes - Lazy loaded */}
                    <Route path="/test" element={<Test />} />
                    <Route path="/product-test" element={<ProductTestPage />} />
                    <Route path="/product-debug" element={<ProductDebugPage />} />
                    <Route path="/product-page-test/:productId" element={<ProductPageTest />} />
                    <Route path="/products-test" element={<ProductsPage />} />
                    <Route path="/consumer-passcode-test" element={<ConsumerPasscodeTest />} />
                    <Route path="/consumer-auth-test" element={<ConsumerAuthTest />} />
                    <Route path="/auth-fix-test" element={<AuthenticationFixTest />} />
                    <Route path="/product-migration" element={<ProductIdMigrationPage />} />
                    <Route path="/supabase-migration" element={<SupabaseMigrationPage />} />
                    <Route path="/supabase-test" element={<SupabaseTestPage />} />
                    <Route path="/system-diagnostics" element={<SystemDiagnosticsPage />} />
                    <Route path="/debug/product-status" element={<ProductStatusTest />} />
                    <Route path="/product-page-diagnostic/:productId" element={<ProductPageDiagnostic />} />
                    <Route path="/product-page-comprehensive-test/:productId" element={<ProductPageComprehensiveTest />} />
                    <Route path="/category-migration-test" element={<CategoryMigrationTestPage />} />
                    <Route path="/location-system-test" element={<LocationSystemTestPage />} />
                    <Route path="/test-northwest-fix" element={<TestNorthwestFix />} />
                    <Route path="/google-maps-test" element={<GoogleMapsTest />} />
                    <Route path="/map-comparison" element={<MapComparisonDemo />} />
                    <Route path="/phone-auth-test" element={<PhoneAuthTest />} />
                    <Route path="/otp-test" element={<OTPTestPage />} />
                    <Route path="/test-shipping-assignment" element={<TestShippingAssignment />} />
                    <Route path="/order-security-test" element={<OrderSecurityTestPage />} />

                    {/* Marketplace Routes - Core routes loaded immediately */}
                    <Route path="/" element={<MarketplaceContainer />} />
                    <Route path="/my-vehicle-parts" element={<MarketplaceContainer />} />
                    <Route path="/wholesale-offers" element={<MarketplaceContainer />} />
                    <Route path="/partners" element={<PartnersPage />} />

                    {/* Consumer Account Routes - Lazy loaded */}
                    <Route path="/wishlist" element={<WishlistPage />} />
                    <Route path="/my-reviews" element={<MyReviewsPage />} />
                    <Route path="/my-orders" element={<MyOrdersPage />} />

            {/* Shipping Company Portal Routes */}
            <Route path="/shipping/login" element={<ShippingLoginPage />} />
            <Route path="/shipping/dashboard" element={<ShippingDashboardPage />} />
            <Route path="/shipping/orders/:orderId" element={<ShippingOrderDetailPage />} />
            <Route path="/shipping" element={<Navigate to="/shipping/login" replace />} />

            {/* ADMIN PANEL TEMPORARILY DISABLED - DO NOT DELETE THESE ROUTES */}
            {/* <Route path="/admin/login" element={<AdminLoginPage />} /> */}
            {/* <Route path="/admin/dashboard" element={<SecureRoute requiredRoles={['admin']}><AdminDashboardPage /></SecureRoute>} /> */}
            {/* <Route path="/admin/orders" element={<SecureRoute requiredRoles={['admin']}><AdminOrdersPage /></SecureRoute>} /> */}
            {/* <Route path="/admin/shipping-companies" element={<SecureRoute requiredRoles={['admin']}><AdminShippingCompaniesPage /></SecureRoute>} /> */}
            {/* <Route path="/admin/shipments" element={<SecureRoute requiredRoles={['admin']}><AdminShipmentsPage /></SecureRoute>} /> */}
            {/* <Route path="/admin/analytics" element={<SecureRoute requiredRoles={['admin']}><AdminAnalyticsPage /></SecureRoute>} /> */}
            {/* <Route path="/admin/users" element={<SecureRoute requiredRoles={['admin']}><AdminUsersPage /></SecureRoute>} /> */}
            {/* <Route path="/admin/settings" element={<SecureRoute requiredRoles={['admin']}><AdminSettingsPage /></SecureRoute>} /> */}
            {/* <Route path="/admin" element={<Navigate to="/admin/login" replace />} /> */}

                    {/* Checkout Routes - Lazy loaded */}
                    <Route path="/basket" element={<BasketPage />} />
                    <Route path="/order-success" element={<OrderSuccessPage />} />

                    {/* Debug Pages - Lazy loaded */}
                    <Route path="/debug" element={<DebugPage />} />
                    <Route path="/order-debug" element={<OrderSystemDebugPage />} />
                    <Route path="/test-order/:orderId" element={
                      <ErrorBoundary>
                        <SupplierOrderDetailPage />
                      </ErrorBoundary>
                    } />
                    <Route path="/simple-test/:orderId" element={<SimpleTestPage />} />

                    {/* Auth Routes - Lazy loaded */}
                    <Route path="/auth/callback" element={<AuthCallbackPage />} />
                    <Route path="/auth/reset-password" element={<ResetPasswordPage />} />

            {/* Legacy Marketplace Route - Redirect to root */}
            <Route path="/marketplace" element={<Navigate to="/" replace />} />

                    {/* Footer and Help Pages - Lazy loaded */}
                    <Route path="/about" element={<AboutPage />} />
                    <Route path="/help" element={<HelpPage />} />
                    <Route path="/contact" element={<Navigate to="/" replace />} />

            {/* Placeholder routes for footer links */}
            <Route path="/careers" element={<Navigate to="/about" replace />} />
            <Route path="/investor-relations" element={<Navigate to="/about" replace />} />
            <Route path="/corporate-responsibility" element={<Navigate to="/about" replace />} />
            <Route path="/delivery-info" element={<Navigate to="/help" replace />} />
            <Route path="/return-policy" element={<Navigate to="/help" replace />} />
            <Route path="/warranty" element={<Navigate to="/help" replace />} />
            <Route path="/installation-services" element={<Navigate to="/help" replace />} />
            <Route path="/privacy-policy" element={<Navigate to="/help" replace />} />
            <Route path="/terms-of-service" element={<Navigate to="/help" replace />} />
            <Route path="/sitemap" element={<Navigate to="/" replace />} />

            {/* Legacy redirects */}
            <Route path="/features" element={<Navigate to="/" replace />} />
            <Route path="/roadmap" element={<Navigate to="/" replace />} />

            {/* Protected App Routes */}
            {/* Main app route redirects to dashboard */}
            <Route path="/app" element={<SecureRoute><Navigate to="/app/dashboard" /></SecureRoute>} />

            {/* Access Denied Page */}
            <Route path="/app/access-denied" element={<AccessDeniedPage />} />

                    {/* Main Application Routes - Lazy loaded */}
                    <Route path="/app/dashboard" element={<SecureRoute><Index /></SecureRoute>} />
                    <Route path="/app/products" element={<SecureRoute requiredRoles={['supplier', 'merchant']}><ProductsPage /></SecureRoute>} />
                    <Route path="/app/products-table" element={<SecureRoute requiredRoles={['supplier', 'merchant']}><Navigate to="/app/products-table/tyres" replace /></SecureRoute>} />
                    <Route path="/app/products-table/:category" element={<SecureRoute requiredRoles={['supplier', 'merchant']}><ProductsDataGridPage /></SecureRoute>} />
                    <Route path="/app/products/new" element={<SecureRoute requiredRoles={['supplier', 'merchant']}><UnifiedProductFormPage /></SecureRoute>} />
                    <Route path="/app/products/edit/:id" element={<SecureRoute requiredRoles={['supplier', 'merchant']}><UnifiedProductFormPage /></SecureRoute>} />
                    <Route path="/app/categories/:slug" element={<SecureRoute requiredRoles={['supplier', 'merchant']}><CategoryProductsPage /></SecureRoute>} />
                    <Route path="/app/compatibility-hub" element={<SecureRoute><CompatibilityHub /></SecureRoute>} />
                    <Route path="/app/orders" element={<SecureRoute requiredRoles={['supplier', 'merchant']}><SupplierOrdersPage /></SecureRoute>} />
                    <Route path="/app/orders/:orderId" element={<SecureRoute requiredRoles={['supplier', 'merchant']}><SupplierOrderDetailPage /></SecureRoute>} />
                    <Route path="/app/shipments" element={<SecureRoute requiredRoles={['supplier', 'merchant']}><SupplierShipmentsPage /></SecureRoute>} />
                    <Route path="/app/customers" element={<SecureRoute requiredRoles={['supplier', 'merchant']}><Index /></SecureRoute>} />

                    <Route path="/app/settings" element={<SecureRoute><SettingsPage /></SecureRoute>} />

            {/* Legacy Supplier Routes - Redirect to new routes */}
            <Route path="/app/supplier" element={<SecureRoute requiredRoles={['supplier']}><Navigate to="/app/dashboard" replace /></SecureRoute>} />
            <Route path="/app/supplier/dashboard" element={<SecureRoute requiredRoles={['supplier']}><Navigate to="/app/dashboard" replace /></SecureRoute>} />
            <Route path="/app/supplier/products" element={<SecureRoute requiredRoles={['supplier']}><Navigate to="/app/products" replace /></SecureRoute>} />
            <Route path="/app/supplier/products-table" element={<SecureRoute requiredRoles={['supplier']}><Navigate to="/app/products-table" replace /></SecureRoute>} />
            <Route path="/app/supplier/products-table/:category" element={<SecureRoute requiredRoles={['supplier']}><ProductsDataGridPage /></SecureRoute>} />
            <Route path="/app/supplier/products/new" element={<SecureRoute requiredRoles={['supplier']}><Navigate to="/app/products/new" replace /></SecureRoute>} />
            <Route path="/app/supplier/products/edit/:id" element={<SecureRoute requiredRoles={['supplier']}><UnifiedProductFormPage /></SecureRoute>} />
            <Route path="/app/supplier/categories/:slug" element={<SecureRoute requiredRoles={['supplier']}><CategoryProductsPage /></SecureRoute>} />
            <Route path="/app/supplier/compatibility-hub" element={<SecureRoute requiredRoles={['supplier']}><Navigate to="/app/compatibility-hub" replace /></SecureRoute>} />

            {/* Legacy Routes - Redirect to new structure */}
            <Route path="/products" element={<SecureRoute><Navigate to="/app/products" replace /></SecureRoute>} />
            <Route path="/products-table" element={<SecureRoute><Navigate to="/app/products-table/tyres" replace /></SecureRoute>} />
            <Route path="/products-table/:category" element={<SecureRoute><ProductsDataGridPage /></SecureRoute>} />
            <Route path="/products/new" element={<SecureRoute><Navigate to="/app/products/new" replace /></SecureRoute>} />
            <Route path="/products/edit/:id" element={<SecureRoute><UnifiedProductFormPage /></SecureRoute>} />
            <Route path="/categories/:slug" element={<SecureRoute><CategoryProductsPage /></SecureRoute>} />

                    {/* Product Page Route - Dynamic product ID (MUST be last before catch-all) */}
                    <Route path="/:productId" element={
                      <ErrorBoundary>
                        <ProductPage />
                      </ErrorBoundary>
                    } />

                    {/* Catch-all route */}
                    <Route path="*" element={<NotFound />} />
                  </Routes>
                </Suspense>

                {/* Auto Authentication Modal for Consumers */}
                <AutoAuthModal
                  delay={45000}
                  triggers={['cart_add', 'checkout', 'profile_access', 'idle_time']}
                  minTimeOnSite={20000}
                  oncePerSession={true}
                />


                </TooltipProvider>
              </CartProvider>
            </LocationProvider>
          </UserProvider>
        </AuthProvider>
      </BrowserRouter>
    </QueryClientProvider>
  );
};

export default App;

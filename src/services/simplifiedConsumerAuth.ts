/**
 * Simplified Consumer Authentication Service with Secure Passcode Support
 *
 * This service provides a simplified consumer registration flow that:
 * 1. Removes SMS verification requirement
 * 2. Provides direct authentication with name + phone + optional passcode
 * 3. Maintains 100% backward compatibility with existing accounts
 * 4. Implements secure passcode storage and validation
 * 5. Ensures proper data integrity and authentication isolation
 */

import { createClient } from '@supabase/supabase-js';
import bcrypt from 'bcryptjs';

// Use standard Supabase client for consumer authentication
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://irkwpzcskeqtasutqnxp.supabase.co';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || '';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

console.log('🔐 [CONSUMER_AUTH] Standard Supabase client initialized for consumer authentication');
console.log('🔐 [CONSUMER_AUTH] URL:', supabaseUrl);
console.log('🔐 [CONSUMER_AUTH] Anon key present:', !!supabaseAnonKey);

export interface SimplifiedAuthRequest {
  fullName: string;
  phone: string;
  passcode?: string; // Optional 4-6 digit passcode for enhanced security
}

export interface SimplifiedAuthResponse {
  success: boolean;
  user?: any;
  profile?: any;
  action?: 'login' | 'signup';
  requiresPasscode?: boolean; // Indicates if user has passcode and needs to enter it
  error?: string;
}

export interface PasscodeValidationResult {
  isValid: boolean;
  error?: string;
}

export interface PasscodeUpdateRequest {
  currentPasscode?: string; // Required if user already has a passcode
  newPasscode: string;
}

export interface PasscodeUpdateResponse {
  success: boolean;
  error?: string;
}

// Passcode attempt tracking for rate limiting
interface PasscodeAttempts {
  count: number;
  lastAttempt: number;
  lockedUntil?: number;
}

const PASSCODE_ATTEMPTS_KEY = 'consumer_passcode_attempts';
const MAX_PASSCODE_ATTEMPTS = 5;
const LOCKOUT_DURATION = 15 * 60 * 1000; // 15 minutes

/**
 * Validate passcode format (4-6 digits)
 */
export function validatePasscodeFormat(passcode: string): PasscodeValidationResult {
  if (!passcode) {
    return { isValid: false, error: 'Passcode is required' };
  }

  if (!/^\d{4,6}$/.test(passcode)) {
    return { isValid: false, error: 'Passcode must be 4-6 digits' };
  }

  return { isValid: true };
}

/**
 * Hash passcode securely using bcrypt
 */
async function hashPasscode(passcode: string): Promise<string> {
  const saltRounds = 12; // High security for passcodes
  return await bcrypt.hash(passcode, saltRounds);
}

/**
 * Verify passcode against hash
 */
async function verifyPasscode(passcode: string, hash: string): Promise<boolean> {
  try {
    return await bcrypt.compare(passcode, hash);
  } catch (error) {
    console.error('Error verifying passcode:', error);
    return false;
  }
}

/**
 * Check if user is locked out due to too many failed attempts
 */
function isPasscodeLocked(phone: string): boolean {
  try {
    const attemptsData = localStorage.getItem(`${PASSCODE_ATTEMPTS_KEY}_${phone}`);
    if (!attemptsData) return false;

    const attempts: PasscodeAttempts = JSON.parse(attemptsData);

    if (attempts.lockedUntil && Date.now() < attempts.lockedUntil) {
      return true;
    }

    return false;
  } catch (error) {
    console.error('Error checking passcode lock:', error);
    return false;
  }
}

/**
 * Record failed passcode attempt
 */
function recordFailedPasscodeAttempt(phone: string): void {
  try {
    const key = `${PASSCODE_ATTEMPTS_KEY}_${phone}`;
    const attemptsData = localStorage.getItem(key);

    let attempts: PasscodeAttempts = attemptsData
      ? JSON.parse(attemptsData)
      : { count: 0, lastAttempt: 0 };

    attempts.count += 1;
    attempts.lastAttempt = Date.now();

    // Lock account if too many attempts
    if (attempts.count >= MAX_PASSCODE_ATTEMPTS) {
      attempts.lockedUntil = Date.now() + LOCKOUT_DURATION;
    }

    localStorage.setItem(key, JSON.stringify(attempts));
  } catch (error) {
    console.error('Error recording failed passcode attempt:', error);
  }
}

/**
 * Clear passcode attempts on successful login
 */
function clearPasscodeAttempts(phone: string): void {
  try {
    localStorage.removeItem(`${PASSCODE_ATTEMPTS_KEY}_${phone}`);
  } catch (error) {
    console.error('Error clearing passcode attempts:', error);
  }
}

/**
 * Validate Algerian phone number format
 */
export function validateAlgerianPhone(phone: string): { isValid: boolean; formatted?: string; error?: string } {
  // Remove all non-digit characters
  const cleanPhone = phone.replace(/\D/g, '');
  
  // Check if it's a valid Algerian number
  if (cleanPhone.length === 9 && cleanPhone.startsWith('5')) {
    // Format: +213XXXXXXXXX
    return {
      isValid: true,
      formatted: `+213${cleanPhone}`
    };
  } else if (cleanPhone.length === 10 && cleanPhone.startsWith('05')) {
    // Format: 05XXXXXXXX -> +213XXXXXXXXX
    return {
      isValid: true,
      formatted: `+213${cleanPhone.substring(1)}`
    };
  } else if (cleanPhone.length === 12 && cleanPhone.startsWith('213')) {
    // Format: 213XXXXXXXXX -> +213XXXXXXXXX
    return {
      isValid: true,
      formatted: `+${cleanPhone}`
    };
  } else if (cleanPhone.length === 13 && cleanPhone.startsWith('2135')) {
    // Format: +213XXXXXXXXX (already formatted)
    return {
      isValid: true,
      formatted: `+${cleanPhone}`
    };
  }
  
  return {
    isValid: false,
    error: 'Please enter a valid Algerian phone number (e.g., 05XXXXXXXX)'
  };
}

/**
 * Convert full name to privacy-protected display name
 */
export function convertToPrivacyProtectedName(fullName: string): { displayName: string; fullName: string } {
  const trimmedName = fullName.trim();
  
  if (!trimmedName) {
    return { displayName: '', fullName: '' };
  }
  
  const nameParts = trimmedName.split(' ').filter(part => part.length > 0);
  
  if (nameParts.length === 1) {
    // Single name: show first 3 characters + "..."
    const name = nameParts[0];
    const displayName = name.length > 3 ? `${name.substring(0, 3)}...` : name;
    return { displayName, fullName: trimmedName };
  } else {
    // Multiple names: show first name + last initial
    const firstName = nameParts[0];
    const lastInitial = nameParts[nameParts.length - 1].charAt(0).toUpperCase();
    const displayName = `${firstName} ${lastInitial}`;
    return { displayName, fullName: trimmedName };
  }
}

/**
 * Simplified consumer authentication with optional passcode support
 * Maintains 100% backward compatibility with existing accounts
 */
export async function authenticateConsumerSimplified(request: SimplifiedAuthRequest): Promise<SimplifiedAuthResponse> {
  try {
    console.log('🚀 Starting simplified consumer authentication for:', request.phone);

    // Validate phone number
    const phoneValidation = validateAlgerianPhone(request.phone);
    if (!phoneValidation.isValid) {
      return {
        success: false,
        error: phoneValidation.error || 'Invalid phone number format'
      };
    }

    const formattedPhone = phoneValidation.formatted!;

    // Validate full name
    if (!request.fullName || request.fullName.trim().length < 2) {
      return {
        success: false,
        error: 'Please enter your full name (at least 2 characters)'
      };
    }

    // Validate passcode format if provided
    if (request.passcode) {
      const passcodeValidation = validatePasscodeFormat(request.passcode);
      if (!passcodeValidation.isValid) {
        return {
          success: false,
          error: passcodeValidation.error
        };
      }
    }

    // Check if user is locked out due to failed passcode attempts
    if (isPasscodeLocked(formattedPhone)) {
      return {
        success: false,
        error: 'Account temporarily locked due to too many failed passcode attempts. Please try again in 15 minutes.'
      };
    }

    // Check if consumer already exists
    console.log('🔍 Checking if consumer exists for phone:', formattedPhone);
    const { data: existingProfiles, error: checkError } = await supabase
      .from('profiles')
      .select('id, full_name, phone_verified, created_at, passcode_hash')
      .eq('phone', formattedPhone)
      .eq('role', 'consumer')
      .limit(1);

    if (checkError) {
      console.error('❌ Error checking existing consumer:', checkError);
      console.error('❌ Error details:', {
        message: checkError.message,
        details: checkError.details,
        hint: checkError.hint,
        code: checkError.code
      });
      return {
        success: false,
        error: 'Failed to verify account. Please try again.'
      };
    }
    
    let userId: string;
    let action: 'login' | 'signup';

    if (existingProfiles && existingProfiles.length > 0) {
      // EXISTING CONSUMER: Handle passcode validation and login
      const existingProfile = existingProfiles[0];
      userId = existingProfile.id;
      action = 'login';

      console.log('✅ Found existing consumer:', userId);

      // Check if user has a passcode set
      if (existingProfile.passcode_hash) {
        // User has passcode - must provide it
        if (!request.passcode) {
          return {
            success: false,
            requiresPasscode: true,
            error: 'Please enter your passcode'
          };
        }

        // Verify passcode
        const isPasscodeValid = await verifyPasscode(request.passcode, existingProfile.passcode_hash);
        if (!isPasscodeValid) {
          recordFailedPasscodeAttempt(formattedPhone);
          return {
            success: false,
            error: 'Invalid passcode. Please try again.'
          };
        }

        // Clear failed attempts on successful passcode verification
        clearPasscodeAttempts(formattedPhone);
      }
      // If no passcode_hash, allow login without passcode (backward compatibility)

      // Update consumer profile with latest info
      const { displayName, fullName } = convertToPrivacyProtectedName(request.fullName.trim());

      const { error: updateError } = await supabase
        .from('profiles')
        .update({
          full_name: displayName,
          original_full_name: fullName,
          phone_verified: true,
          phone_verified_at: new Date().toISOString(),
          last_login: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (updateError) {
        console.error('❌ Error updating consumer profile:', updateError);
        console.error('❌ Update error details:', {
          message: updateError.message,
          details: updateError.details,
          hint: updateError.hint,
          code: updateError.code
        });
        // Continue anyway - login is more important than update
      }
      
    } else {
      // NEW CONSUMER: Create account with optional passcode
      userId = crypto.randomUUID();
      action = 'signup';

      console.log('🆕 Creating new consumer:', userId);

      // For new accounts, passcode is required if provided
      if (request.passcode) {
        const passcodeValidation = validatePasscodeFormat(request.passcode);
        if (!passcodeValidation.isValid) {
          return {
            success: false,
            error: passcodeValidation.error
          };
        }
      }

      const { displayName, fullName } = convertToPrivacyProtectedName(request.fullName.trim());

      // Hash passcode if provided
      let passcodeHash: string | undefined;
      if (request.passcode) {
        passcodeHash = await hashPasscode(request.passcode);
      }

      const profileData: any = {
        id: userId,
        phone: formattedPhone,
        role: 'consumer',
        full_name: displayName,
        original_full_name: fullName,
        phone_verified: true,
        phone_verification_method: 'simplified_auth',
        phone_verified_at: new Date().toISOString(),
        last_login: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      // Add passcode hash if provided
      if (passcodeHash) {
        profileData.passcode_hash = passcodeHash;
      }

      const { error: insertError } = await supabase
        .from('profiles')
        .insert(profileData);

      if (insertError) {
        console.error('❌ Error creating consumer profile:', insertError);
        console.error('❌ Insert error details:', {
          message: insertError.message,
          details: insertError.details,
          hint: insertError.hint,
          code: insertError.code
        });
        return {
          success: false,
          error: 'Failed to create account. Please try again.'
        };
      }
    }
    
    // Fetch the complete profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (profileError || !profile) {
      console.error('❌ Error fetching consumer profile:', profileError);
      console.error('❌ Profile fetch error details:', {
        message: profileError?.message,
        details: profileError?.details,
        hint: profileError?.hint,
        code: profileError?.code
      });
      return {
        success: false,
        error: 'Authentication failed. Please try again.'
      };
    }
    
    // Create phone session for consumer auth
    const sessionResult = await createSimplifiedPhoneSession(userId, formattedPhone, profile);
    
    if (!sessionResult.success) {
      return {
        success: false,
        error: 'Failed to create session. Please try again.'
      };
    }
    
    console.log(`✅ Simplified consumer authentication ${action} successful for:`, userId);
    
    return {
      success: true,
      user: sessionResult.user,
      profile: sessionResult.profile,
      action
    };
    
  } catch (error) {
    console.error('❌ Error in simplified consumer authentication:', error);
    return {
      success: false,
      error: 'Authentication failed. Please try again.'
    };
  }
}

/**
 * Create simplified phone session (stores in localStorage)
 */
async function createSimplifiedPhoneSession(userId: string, phone: string, profile: any): Promise<{ success: boolean; user?: any; profile?: any }> {
  try {
    console.log('🔐 Creating simplified phone session for user:', userId);
    
    // Create user object for session
    const user = {
      id: userId,
      phone: phone,
      role: 'consumer',
      aud: 'authenticated',
      created_at: profile.created_at,
      updated_at: profile.updated_at
    };
    
    // Store session in localStorage
    const sessionData = {
      user,
      profile,
      expires_at: Date.now() + (30 * 24 * 60 * 60 * 1000), // 30 days
      created_at: Date.now()
    };
    
    localStorage.setItem('phone_auth_session', JSON.stringify(sessionData));
    clearSessionCache(); // Clear cache when session changes

    console.log('✅ Simplified phone session created successfully');
    
    return {
      success: true,
      user,
      profile
    };
    
  } catch (error) {
    console.error('❌ Error creating simplified phone session:', error);
    return { success: false };
  }
}

// Cache for session to avoid repeated localStorage reads
let sessionCache: { session: any; timestamp: number } | null = null;
const CACHE_DURATION = 5000; // 5 seconds cache

/**
 * Get cached session or read from localStorage (PERFORMANCE OPTIMIZED)
 */
function getCachedSession(): any | null {
  try {
    // Use cache if it's fresh (within 5 seconds)
    if (sessionCache && (Date.now() - sessionCache.timestamp) < CACHE_DURATION) {
      return sessionCache.session;
    }

    const phoneSession = localStorage.getItem('phone_auth_session');
    if (!phoneSession) {
      sessionCache = { session: null, timestamp: Date.now() };
      return null;
    }

    const session = JSON.parse(phoneSession);

    // Check if session is expired
    if (session.expires_at && Date.now() > session.expires_at) {
      localStorage.removeItem('phone_auth_session');
      sessionCache = { session: null, timestamp: Date.now() };
      return null;
    }

    // Cache the valid session
    sessionCache = { session, timestamp: Date.now() };
    return session;

  } catch (error) {
    console.error('Error getting cached session:', error);
    sessionCache = { session: null, timestamp: Date.now() };
    return null;
  }
}

/**
 * Clear session cache (call when session changes)
 */
function clearSessionCache(): void {
  sessionCache = null;
}

/**
 * Check if consumer is authenticated (PERFORMANCE OPTIMIZED)
 */
export function isConsumerAuthenticated(): boolean {
  const session = getCachedSession();
  return session?.profile?.role === 'consumer' || false;
}

/**
 * Get current consumer session (PERFORMANCE OPTIMIZED)
 */
export function getCurrentConsumerSession(): { user: any; profile: any } | null {
  const session = getCachedSession();

  if (session?.profile?.role === 'consumer') {
    return {
      user: session.user,
      profile: session.profile
    };
  }

  return null;
}

/**
 * Logout consumer (PERFORMANCE OPTIMIZED)
 */
export function logoutConsumer(): void {
  try {
    localStorage.removeItem('phone_auth_session');
    clearSessionCache(); // Clear cache when session changes
    console.log('✅ Consumer logged out successfully');
  } catch (error) {
    console.error('Error logging out consumer:', error);
  }
}

/**
 * Update consumer passcode (for logged-in users only)
 */
export async function updateConsumerPasscode(request: PasscodeUpdateRequest): Promise<PasscodeUpdateResponse> {
  try {
    // Check if user is authenticated
    const session = getCurrentConsumerSession();
    if (!session) {
      return {
        success: false,
        error: 'You must be logged in to update your passcode'
      };
    }

    const userId = session.user.id;
    const phone = session.user.phone;

    // Validate new passcode format
    const passcodeValidation = validatePasscodeFormat(request.newPasscode);
    if (!passcodeValidation.isValid) {
      return {
        success: false,
        error: passcodeValidation.error
      };
    }

    // Check if user is locked out
    if (isPasscodeLocked(phone)) {
      return {
        success: false,
        error: 'Account temporarily locked due to too many failed attempts. Please try again in 15 minutes.'
      };
    }

    // Get current profile to check if user already has a passcode
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('passcode_hash')
      .eq('id', userId)
      .single();

    if (profileError) {
      console.error('❌ Error fetching user profile:', profileError);
      return {
        success: false,
        error: 'Failed to verify account. Please try again.'
      };
    }

    // If user already has a passcode, verify current passcode
    if (profile.passcode_hash) {
      if (!request.currentPasscode) {
        return {
          success: false,
          error: 'Current passcode is required to set a new passcode'
        };
      }

      const isCurrentPasscodeValid = await verifyPasscode(request.currentPasscode, profile.passcode_hash);
      if (!isCurrentPasscodeValid) {
        recordFailedPasscodeAttempt(phone);
        return {
          success: false,
          error: 'Current passcode is incorrect'
        };
      }
    }

    // Hash new passcode
    const newPasscodeHash = await hashPasscode(request.newPasscode);

    // Update passcode in database
    const { error: updateError } = await supabase
      .from('profiles')
      .update({
        passcode_hash: newPasscodeHash,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (updateError) {
      console.error('❌ Error updating passcode:', updateError);
      return {
        success: false,
        error: 'Failed to update passcode. Please try again.'
      };
    }

    // Clear any failed attempts on successful update
    clearPasscodeAttempts(phone);

    console.log('✅ Consumer passcode updated successfully');
    return { success: true };

  } catch (error) {
    console.error('❌ Error updating consumer passcode:', error);
    return {
      success: false,
      error: 'Failed to update passcode. Please try again.'
    };
  }
}

/**
 * Check if current user has a passcode set
 */
export async function hasPasscodeSet(): Promise<boolean> {
  try {
    const session = getCurrentConsumerSession();
    if (!session) return false;

    const { data: profile, error } = await supabase
      .from('profiles')
      .select('passcode_hash')
      .eq('id', session.user.id)
      .single();

    if (error) {
      console.error('Error checking passcode status:', error);
      return false;
    }

    return !!profile.passcode_hash;
  } catch (error) {
    console.error('Error checking passcode status:', error);
    return false;
  }
}
